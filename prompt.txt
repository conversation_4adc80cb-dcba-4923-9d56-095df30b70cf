Project title: OLED Display UI Generator
Repo / workspace name: oled-display-ui-generator
Short description: A professional desktop application (PySide6) for designing OLED UIs, creating pixel icons (from image or pixel-editor), previewing on simulated 0.96" and 1.3" OLED displays, and producing fully working, memory-aware, 100% accurate Arduino/ESP code (U8g2/Adafruit/PlatformIO-ready). Includes export of icons as PROGMEM, bitmaps, and ready .ino/PlatformIO projects. Branded with Developed by <PERSON> | SKR Electronics Lab.

Important global instruction (MUST obey):

The app is proprietary (not open-source). Do not create or push any GitHub actions or public repo by default. The agent should produce a local workspace, build artifacts and a packaged installer script. (If asked to create a repo, create a local git repo only; do not publish.)

1) Overall goals (short)

Create a polished, professional, dark-themed desktop app in PySide6 that looks and feels like high-end software (Premiere/Photoshop/Davinci-inspired tone) but with its own identity and a clean workflow.

Provide two main tab-systems at launch:

Icons & Image → Code tab (pixel editor + image import + code export)

OLED UI Designer tab (canvas with templates, drag/drop icons, menu builder, button config, code export)

Support displays: 0.96" (usually 128×64 or 96×64) and 1.3" (128×64) including SH1106 and SSD1306 controllers.

Support targets: Arduino UNO / Nano (AVR), ESP8266, ESP32.

Provide robust code generation engine that outputs correct code for chosen library (U8g2 preferred for SH1106; Adafruit SSD1306 for SSD1306) and includes icons as PROGMEM, button handling, debounce/long-press, and menu logic.

Make everything intuitive for beginners: live simulator, tooltips (short feature info on hover), sample templates, default pin maps, and warnings where user choices may exceed hardware limits.

2) Visual style / theme (exact)

Design inspired by Premiere/Photoshop/Davinci Resolve: dark, contrasty, professional.

Primary palette (choose as default):

Background / primary surface: #0B1226 (very dark blue/indigo)

Panel surface: #122040 (secondary dark)

Primary accent: #0B3D91 (deep blue)

Secondary accent: #00B4D8 (cyan)

Muted text: #AFC9FF

Bright text: #FFFFFF

Highlight / interactive: #6A2AC5 (a subtle purple emphasis)

Error / alert: #FF5C5C

Theme rules:

Use soft shadows, 2xl rounded corners for buttons and cards.

Typography: use modern, legible UI font (system font fallback: Segoe UI / San Francisco / Roboto).

Provide two workspace presets: Default (Designer) and Compact (Editor).

3) High-level app layout & UX (exact)

Window title: OLED Display UI Generator (app icon: vector SVG, optional)

Top: menu + toolbar (New, Open, Save, Save as, Undo, Redo, Export Code, Export Icon, Preview, Settings)

Left dock: Project Explorer / Assets (icons, templates, projects)

Center: Tabbed Canvas area (Tabs: Icons, UI Designer). Canvas shows device frame and zoom controls.

Right dock: Inspector / Properties for selected element (all editable fields like x,y,w,h,font,action,pins)

Bottom: Live Code Preview (Jinja2 output view) + Console / Logs

Footer / Statusbar (always visible): left shows current project and target, right shows: Developed by SK Raihan | SKR Electronics Lab and current simulator resolution (ex: 128×64 (SH1106)).

Dock behavior: Dockable, resizable; save workspace layout to user profile.

4) Core tabs & features (detailed)
A — Icons & Image → Code tab

Purpose: create, edit, import icons/bitmaps and export to code and assets.

Features:

Pixel Editor: select size (8×8, 16×16, 24×24, 32×32), pencil, eraser, fill, rectangle, line, invert, flip H/V, rotate, copy/paste, selection, undo/redo stack (unlimited until memory or 1000 operations).

Image Import: import JPG/PNG; provide options:

Resize + crop to selected icon size

Binarization methods: Threshold, Ordered dithering, Floyd–Steinberg (error diffusion)

Invert / Mirror / Rotate

Edge-preserving smoothing (optional) before binarize

Batch Import: allow importing multiple images as separate icons.

Output formats:

C PROGMEM arrays (row-major, MSB-first) with width/height metadata and a stable naming scheme: ICON_<NAME>_<W>x<H>. Include comment lines describing bit ordering and which library it is compatible with.

XBM and 1-bit BMP exports

.json asset entry to store in project

Save to Assets: add icon to project Assets with metadata and preview thumbnails.

Preview pane: shows scaled preview at 1x/2x/4x/8x, and how it looks when drawn using chosen library (Adafruit vs u8g2).

Export buttons: Export C (single icon), Export All Icons (assets.h), Copy to Clipboard

Tooltips: every control has hover text of one sentence describing the feature.

Accessibility: keyboard shortcuts, zoom with mouse wheel, pan with middle drag.

B — OLED UI Designer tab

Purpose: visually create full OLED UI screens, menus, headers, and generate ready code.

Features:

Device selection: dropdown to pick 0.96" (SH1106/SSD1306/96x64) or 1.3" (SH1106/128x64) and exact controller. Also choose I²C address and pins.

Templates gallery: several starter templates (Header+Menu, Dashboard, IconGrid, Compact Menu, Fullscreen Menu). Clicking a template loads it to canvas.

Drag & Drop: drag icons from Assets to canvas; move & align; multi-select; duplicate.

Menu Builder: add menu items (text + optional icon) and set visible rows; choose selection style (invert, arrow, underline), choice of scrollbar vs page indicator, and item spacing.

Header: add title text, battery icon (auto or manual value), WiFi and Bluetooth status icons.

Buttons config: UI to set 2..5 buttons and map them to directions/actions (Up, Down, Left, Right, OK). For each button allow setting pin number and polarity (active_low/default = true).

Preview & simulate: preview menu scroll animations; simulate button presses using keyboard keys (Up/Down/Left/Right/Enter) to preview behavior.

Properties inspector: everything editable — fonts (u8g2 font choices), font sizes, left padding, scroll speed, debounce/long-press config, selection color, icons.

Asset usage: add icon overlays, assign icons to menu items, use saved icons.

Live Code Preview: shows generated C++/Arduino code for current canvas; updates live as user edits.

Export: Export Project (JSON), Export Arduino project (.ino & assets.h), Export PlatformIO project (optional), Export single assets.h.

Undo/Redo, Snap & Grid: snap to 1px grid (toggle), show grid overlay, align & distribute tools (align left/center/right/top/bottom).

Search: search menu items & assets.

Feature hints: hovering over any checkbox/setting shows a short explanation (e.g., "Page buffer reduces RAM usage on AVR; use when full buffer won't fit").

5) Supported hardware & libraries (initial release)

Displays: 128×64 SH1106 (1.3"), 128×64 SSD1306 (1.3"), 96×64 SH1106/SSD1306 (0.96"). (Support both controllers; choose correct template.)

Libraries:

U8g2 (primary; works with SH1106 + SSD1306)

Adafruit_SSD1306 + Adafruit_GFX (for SSD1306)

Target boards:

Arduino UNO / Nano (AVR) — special page-buffer templates & memory estimation logic

ESP8266

ESP32

Buttons: support 2 to 5 buttons with configurable pin numbers and active polarity.

6) Code generation engine: architecture & behavior (in-depth — MUST follow)
A — Core design

Use pydantic (or equivalent typed models) for canonical Project schema (display, target, buttons, UI elements, assets). The UI only edits instances of this schema.

Use Jinja2 (with StrictUndefined) for templates. Each template is named with target metadata, e.g. u8g2_sh1106_128x64_esp32.ino.jinja.

Include assets.h.jinja for PROGMEM arrays. Use u8g2 or Adafruit pattern as required by target.

Provide a CodeGenerator class with API:

class CodeGenerator:
    def __init__(self, project: Project, template_name: str, out_dir: Path, options: dict = None): ...
    def validate(self) -> List[str]:  # errors/warnings
    def estimate_memory(self) -> Dict[str, int]  # frame buffer bytes, asset bytes, estimated total RAM
    def generate(self) -> Path  # writes project files to out_dir


Generated project structure (for Arduino .ino export):

<project_name>_export/
  ├── main.ino
  ├── assets.h
  ├── README_EXPORT.md
  └── platformio.ini (optional)

B — Memory estimation & fallback logic (MUST implement)

Framebuffer bytes = (width * height) / 8.

Sum icon bytes = sum( (w * h) / 8 rounded up per icon ).

Add estimated overhead: strings, local buffers (configurable) — use conservative defaults (AVR UNO RAM = 2048 bytes; reserve 30% safety margin).

If (framebuffer + assets + overhead) > 75% of target RAM and target is AVR UNO/Nano, auto-fallback to page-buffer template and warn user in README_EXPORT.md and UI console. Provide exact numbers and explanation in the README.

If template for exact combination not present, generator returns a clear error and lists supported templates.

C — Button & input code generation (MUST be robust)

Generate constants at top for pins and active polarity. Provide options for INPUT_PULLUP default (active low) and also allow PULLDOWN config for ESP32 if desired.

Provide built-in debounce, long-press detection and auto-repeat logic. Put debounce/long-press values as constants user can change at top of generated file.

Provide code hooks for per-menu action_name (generate void action_<name>() { /* TODO: implement */ } stubs with inline comments).

Use a simple state machine for menu navigation to keep generated code readable.

D — Icon / PROGMEM generation (MUST)

Icons exported as const uint8_t PROGMEM icon_name[] = { 0x00, 0xFF, ... }; with a small header comment: width/height/ordering. Use row-major, MSB-first bit ordering (documented).

Generator must produce utility functions or macros for drawing depending on library:

For u8g2: show function example for drawing: u8g2.drawXBMP(x, y, w, h, icon_name); or u8g2.drawBitmap() depending on template convention; include correct usage in code.

For Adafruit: display.drawBitmap(x, y, icon_name, w, h, 1);

Include a assets.h file with all icons and comments.

E — Output code style & comments (MUST)

Every generated file must include header:

// Project: <project_name>
// Generated by OLED Display UI Generator vX.Y.Z
// Developed by SK Raihan | SKR Electronics Lab
// Generated on: <YYYY-MM-DD>


Inline comments: The user requested inline comments in most code lines so they can understand and modify. The agent must produce code with:

Clear function-level header comments (what it does, inputs, outputs)

Brief inline comments near code blocks explaining logic (but avoid redundant comments that state the obvious). The priority is clarity, not comment spam.

Provide a README_EXPORT.md inside each export with:

brief explanation of chosen template & library

estimated RAM usage and whether page-mode was chosen

how to compile/upload using Arduino IDE and PlatformIO

required library versions and includes

F — Validation & optional compile

If arduino-cli or platformio is present and configured in the developer machine, the generator may optionally run a local compile in a sandboxed process and collect errors/warnings and show them in UI. If not present, run a static check: ensure setup() and loop() exist, includes are present, and no unresolved Jinja tokens are in output.

Log all warnings and errors to UI console.

7) Pixel conversion & image processing (details)

Conversions must support different dithering algorithms:

Threshold (user-set threshold 0..255)

Ordered dithering (Bayer matrix)

Floyd–Steinberg (error diffusion)

Provide pre-processing: resize + grayscale smoothing + unsharp mask (optional) to improve recognition before threshold.

Allow user to set bit-ordering and byte-packing (defaults set to row-major MSB-first).

Provide a preview showing actual pixels and a “how it looks on target” view (using selected library draw function simulation).

8) UX polish & helpful features (must-haves)

Tooltips/hints for all controls (one-line explanation). Example: hovering on “Page Buffer” setting shows: “Page buffer reduces RAM usage on AVR boards — use if full buffer doesn’t fit”.

Onboarding modal with 3-step quickstart: Create Project → Import Icon → Export Code.

Context-aware validation: if user sets font too large for visible rows, show a warning and auto-suggest visible_rows.

Keyboard shortcuts (list in Help): Ctrl+S save, Ctrl+Z undo, Ctrl+Y redo, Ctrl+E export, arrow keys nudge selection, Enter to simulate OK, Up/Down to simulate navigation in preview.

Undo/Redo for canvas and pixel-editor (Command pattern), with infinite but practical limits.

Asset search & tagging (tag icons as user sorts them).

Export presets: quick export for common combos (e.g., “ESP32 + SH1106 + U8g2”).

9) File structure & module responsibilities (agent must produce this workspace)
oled-display-ui-generator/
├── build_scripts/                # PyInstaller build scripts (local, not CI)
├── docs/
│   ├── user_guide.md
│   └── design.md
├── examples/                      # example projects & exports
│   ├── demo_sh1106_esp32/
│   ├── demo_sh1106_uno/
│   └── demo_ssd1306_uno/
├── src/
│   └── oled_ui_generator/
│       ├── __init__.py
│       ├── main.py               # CLI + app launcher (python -m oled_ui_generator)
│       ├── app.py                # Application bootstrap & theme loader
│       ├── window.py             # MainWindow, menus, toolbar, docks
│       ├── workspace.py          # save/load workspace state
│       ├── ui/
│       │   ├── canvas_widget.py  # OLED simulator + drawing helpers
│       │   ├── pixel_editor.py   # pixel editor widget with undo/redo
│       │   ├── assets_panel.py
│       │   ├── inspector.py
│       │   └── templates_browser.py
│       ├── models/
│       │   ├── project.py        # pydantic models, JSON (de)serializers
│       │   └── elements.py
│       ├── codegen/
│       │   ├── generator.py      # CodeGenerator class (public API)
│       │   ├── validators.py
│       │   └── templates/        # jinja2 templates for all supported combos
│       └── util/
│           ├── image_utils.py
│           ├── bitmap_utils.py   # conversion functions and tests
│           └── ui_helpers.py
├── requirements.txt
├── README.md
├── LICENSE.txt  # "Proprietary — All rights reserved" or leave blank per your policy
└── DELIVERABLES_SUMMARY.md

10) Deliverables & milestones (numbered; agent must deliver them in order)

Deliver as a local workspace. For each milestone deliver the code, a short changelog for that milestone, and instructions to run it.

Milestone 1 — App skeleton & theme

PySide6 app that runs: python -m oled_ui_generator.main opens a window with toolbar, left Assets dock, center placeholder canvas, right inspector, bottom console and footer Developed by SK Raihan | SKR Electronics Lab.

Dark-blue theme applied.

Milestone 2 — Pixel editor

Fully working pixel editor with 8/16/24/32 sizes, undo/redo, import image, dithering options, and export to PROGMEM / XBM / JSON. Add asset save to project.

Milestone 3 — Canvas & UI Designer

Canvas with 128×64 & 96×64 modes, drag-drop icons, templates gallery, menu builder, header builder, and simulation of basic scrolling. Inspector edits properties reflect on canvas.

Milestone 4 — Code generation engine

CodeGenerator implemented with Jinja2 templates for:

U8g2 + SH1106 + ESP32 (full-buffer)

U8g2 + SH1106 + UNO (page-buffer fallback)

Adafruit_SSD1306 + UNO

generate() produces main.ino, assets.h and README_EXPORT.md. Include memory check & fallback to page-buffer for AVR.

Milestone 5 — Polishing & packaging

Add live code preview panel, tooltips, keyboard shortcuts, settings panel, export UI, and PyInstaller build script for Windows/Linux/macOS, and user guide README.md.

Milestone 6 — Tests & examples

Include unit tests for bitmap generation and template rendering (pytest). Put example exports in /examples.

11) Testing & acceptance criteria (what “works” looks like)

Running python -m oled_ui_generator.main opens the app and shows the footer Developed by SK Raihan | SKR Electronics Lab. (Milestone 1 success criterion)

Pixel Editor: import test.png → choose 16×16 + Floyd–Steinberg → export PROGMEM → import that PROGMEM icon to assets and place on canvas — the icon visually matches the preview and exported bytes match the simulator rendering. (Milestone 2)

CodeGen: Use the provided sample project fixture (simple header+menu) and export for ESP32 + SH1106 + U8g2 — generated main.ino must contain #include <U8g2lib.h>, have setup() and loop(), include assets.h, and include Developed by SK Raihan | SKR Electronics Lab in the header comment. (Milestone 4)

Memory fallback: Simulate a project where icons + buffer exceed UNO RAM; generator should automatically produce page-mode template and include a clear explanatory warning in README_EXPORT.md. (Milestone 4)

All UI controls have one-line tooltips and are keyboard accessible.

Provide 3 example exported projects in /examples.

12) Additional quality instructions for the AI (code style & comments)

Language & versions: Use Python 3.11+; pin dependencies in requirements.txt.

Code style: snake_case for functions, CamelCase for classes. Type hints everywhere. Use descriptive names.

Inline comments: Add helpful inline comments and docstrings. The user explicitly requested inline comments in most lines of code — follow this, but prefer useful comments (explain why, not what the language already makes obvious). Ensure code remains readable and not cluttered.

Separation of concerns: GUI separate from model and generator code.

Testing: Add pytest tests for generator templates and bitmap conversion functions.

No external telemetry: do not call external APIs or send any user data.

13) Error cases & fallbacks (explicit)

If user selects an unsupported display/library combination: show a clear dialog listing supported combinations and how to request support.

If generator finds unresolved Jinja placeholders: fail with explicit error and show line/variable name.

If platform-specific compile tools (arduino-cli or platformio) are not installed: show a friendly message and instructions to install them; do not attempt to auto-install.

If memory estimate fails: provide a conservative fail-safe (use page-mode for AVR) and explain.

14) Exported code sample header (must be included at top of every generated file)
// Project: {{ project.project_name }}
// Generated by OLED Display UI Generator v{{ app.version }}
// Developed by SK Raihan | SKR Electronics Lab
// Generated on: {{ generation_date }}

15) Helpful developer notes (for the AI building the app)

Provide a small “developer guide” file in /docs/developer_notes.md explaining where to change templates, how to add a new target library, and how to add a theme color.

Keep templates minimal and readable — prefer readability and comments in generated C++ code.

Use Jinja2 StrictUndefined to catch missing variables early.

Provide a config.json that lists supported templates (so future adding new boards is straightforward).

16) Final deliverable format (how the AI should deliver results)

Create a zip of the workspace oled-display-ui-generator.zip containing the full src/, docs/, examples/, build_scripts/, and requirements.txt. No remote repo required.

Provide a short DELIVERABLES_SUMMARY.md listing what was implemented, what's pending (if anything), and how to run the app locally:

python -m venv venv

pip install -r requirements.txt

python -m oled_ui_generator.main

For each milestone, include a short test report (what was tested and results).

17) One last user-facing requirement (UX)

Hover & short help: every setting should display a one-line summary on hover explaining what it does (so non-experts learn without reading docs).

Keep the UI minimal on first run: show a 3-step quickstart overlay: (1) Create new project → (2) Import icon or pick template → (3) Export code.

End of prompt — explicit instruction to the agent:

Start now: build the workspace according to this spec. Deliver it in ordered milestones (1 → 6). For each milestone include a short changelog and instructions to run the milestone. Ensure the app is local-only (do not publish to GitHub). Prioritize correctness, helpful inline comments, and clear in-app explanations for every feature. Remember to include the footer string: Developed by SK Raihan | SKR Electronics Lab everywhere required. Remember that you should test the app as you can using the command prompt that the app features are working or not. I know testing everything is not possible but do as you can. Start your work All the best.