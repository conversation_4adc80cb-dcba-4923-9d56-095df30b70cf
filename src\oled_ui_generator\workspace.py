"""
Workspace management for saving and loading application state
Developed by <PERSON> | SKR Electronics Lab
"""

import json
from pathlib import <PERSON>
from typing import Dict, Any, Optional
from PySide6.QtCore import QSettings


class WorkspaceManager:
    """Manages workspace state including window layout and project settings"""
    
    def __init__(self):
        """Initialize the workspace manager"""
        self.settings = QSettings()
        self.current_workspace = "Default"
        
        # Default workspace configurations
        self.workspace_presets = {
            "Default": {
                "name": "Default (Designer)",
                "description": "Standard layout optimized for UI design",
                "dock_layout": "default"
            },
            "Compact": {
                "name": "Compact (Editor)", 
                "description": "Compact layout optimized for icon editing",
                "dock_layout": "compact"
            }
        }
    
    def save_workspace_state(self, window_geometry: bytes, window_state: bytes, 
                           dock_states: Dict[str, Any]) -> None:
        """Save the current workspace state"""
        workspace_key = f"workspace_{self.current_workspace}"
        
        workspace_data = {
            "geometry": window_geometry.hex(),  # Convert bytes to hex string
            "state": window_state.hex(),
            "dock_states": dock_states,
            "timestamp": self._get_current_timestamp()
        }
        
        # Save to QSettings
        self.settings.setValue(workspace_key, json.dumps(workspace_data))
        self.settings.sync()
    
    def load_workspace_state(self, workspace_name: Optional[str] = None) -> Dict[str, Any]:
        """Load workspace state"""
        if workspace_name:
            self.current_workspace = workspace_name
        
        workspace_key = f"workspace_{self.current_workspace}"
        workspace_json = self.settings.value(workspace_key, "{}")
        
        try:
            workspace_data = json.loads(workspace_json)
            
            # Convert hex strings back to bytes
            if "geometry" in workspace_data:
                workspace_data["geometry"] = bytes.fromhex(workspace_data["geometry"])
            if "state" in workspace_data:
                workspace_data["state"] = bytes.fromhex(workspace_data["state"])
            
            return workspace_data
        except (json.JSONDecodeError, ValueError):
            return {}
    
    def get_available_workspaces(self) -> Dict[str, Dict[str, str]]:
        """Get list of available workspace presets"""
        return self.workspace_presets.copy()
    
    def create_custom_workspace(self, name: str, description: str) -> bool:
        """Create a new custom workspace"""
        if name in self.workspace_presets:
            return False  # Workspace already exists
        
        self.workspace_presets[name] = {
            "name": name,
            "description": description,
            "dock_layout": "custom"
        }
        
        # Save to settings
        self.settings.setValue("custom_workspaces", json.dumps(self.workspace_presets))
        return True
    
    def delete_workspace(self, name: str) -> bool:
        """Delete a custom workspace (cannot delete default presets)"""
        if name in ["Default", "Compact"]:
            return False  # Cannot delete default presets
        
        if name in self.workspace_presets:
            del self.workspace_presets[name]
            
            # Remove from settings
            workspace_key = f"workspace_{name}"
            self.settings.remove(workspace_key)
            self.settings.setValue("custom_workspaces", json.dumps(self.workspace_presets))
            return True
        
        return False
    
    def switch_workspace(self, name: str) -> bool:
        """Switch to a different workspace"""
        if name in self.workspace_presets:
            self.current_workspace = name
            self.settings.setValue("current_workspace", name)
            return True
        return False
    
    def get_current_workspace(self) -> str:
        """Get the name of the current workspace"""
        return self.current_workspace
    
    def load_user_preferences(self) -> Dict[str, Any]:
        """Load user preferences and settings"""
        prefs_json = self.settings.value("user_preferences", "{}")
        
        try:
            preferences = json.loads(prefs_json)
        except json.JSONDecodeError:
            preferences = {}
        
        # Set default preferences if not present
        default_prefs = {
            "theme": "dark_blue",
            "auto_save": True,
            "auto_save_interval": 300,  # 5 minutes
            "show_grid": True,
            "snap_to_grid": True,
            "grid_size": 1,
            "show_tooltips": True,
            "recent_projects": [],
            "default_export_path": str(Path.home() / "Documents" / "OLED_Projects"),
            "compiler_path": "",
            "platformio_path": ""
        }
        
        # Merge with defaults
        for key, value in default_prefs.items():
            if key not in preferences:
                preferences[key] = value
        
        return preferences
    
    def save_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """Save user preferences"""
        self.settings.setValue("user_preferences", json.dumps(preferences))
        self.settings.sync()
    
    def add_recent_project(self, project_path: str) -> None:
        """Add a project to the recent projects list"""
        preferences = self.load_user_preferences()
        recent_projects = preferences.get("recent_projects", [])
        
        # Remove if already in list
        if project_path in recent_projects:
            recent_projects.remove(project_path)
        
        # Add to beginning
        recent_projects.insert(0, project_path)
        
        # Keep only last 10 projects
        recent_projects = recent_projects[:10]
        
        preferences["recent_projects"] = recent_projects
        self.save_user_preferences(preferences)
    
    def get_recent_projects(self) -> list:
        """Get list of recent projects"""
        preferences = self.load_user_preferences()
        recent_projects = preferences.get("recent_projects", [])
        
        # Filter out projects that no longer exist
        existing_projects = []
        for project_path in recent_projects:
            if Path(project_path).exists():
                existing_projects.append(project_path)
        
        # Update the list if any projects were removed
        if len(existing_projects) != len(recent_projects):
            preferences["recent_projects"] = existing_projects
            self.save_user_preferences(preferences)
        
        return existing_projects
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp as string"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def export_workspace(self, workspace_name: str, export_path: Path) -> bool:
        """Export workspace configuration to file"""
        try:
            workspace_data = self.load_workspace_state(workspace_name)
            
            if not workspace_data:
                return False
            
            # Add metadata
            export_data = {
                "workspace_name": workspace_name,
                "workspace_config": self.workspace_presets.get(workspace_name, {}),
                "workspace_state": workspace_data,
                "export_timestamp": self._get_current_timestamp(),
                "app_version": "1.0.0"
            }
            
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Error exporting workspace: {e}")
            return False
    
    def import_workspace(self, import_path: Path) -> Optional[str]:
        """Import workspace configuration from file"""
        try:
            with open(import_path, 'r') as f:
                import_data = json.load(f)
            
            workspace_name = import_data.get("workspace_name")
            workspace_config = import_data.get("workspace_config", {})
            workspace_state = import_data.get("workspace_state", {})
            
            if not workspace_name:
                return None
            
            # Add to workspace presets
            self.workspace_presets[workspace_name] = workspace_config
            
            # Save workspace state
            if workspace_state:
                workspace_key = f"workspace_{workspace_name}"
                self.settings.setValue(workspace_key, json.dumps(workspace_state))
            
            # Update custom workspaces
            self.settings.setValue("custom_workspaces", json.dumps(self.workspace_presets))
            self.settings.sync()
            
            return workspace_name
        except Exception as e:
            print(f"Error importing workspace: {e}")
            return None
