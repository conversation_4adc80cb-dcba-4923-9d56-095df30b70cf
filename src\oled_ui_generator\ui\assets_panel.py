"""
Assets panel for managing icons, templates, and project resources
Developed by <PERSON>han | SKR Electronics Lab
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, 
    QTreeWidgetItem, QPushButton, QLabel, QLineEdit,
    QGroupBox, QScrollArea
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QPixmap


class AssetsPanel(QWidget):
    """Panel for managing project assets including icons and templates"""
    
    # Signals for asset selection and management
    asset_selected = Signal(str)  # Asset ID selected
    template_selected = Signal(str)  # Template selected
    
    def __init__(self):
        """Initialize the assets panel"""
        super().__init__()
        self._setup_ui()
        self._populate_default_content()
    
    def _setup_ui(self):
        """Set up the user interface for the assets panel"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # Search bar
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search assets...")
        self.search_input.textChanged.connect(self._filter_assets)
        search_layout.addWidget(QLabel("Search:"))
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        
        # Assets tree widget
        self.assets_tree = QTreeWidget()
        self.assets_tree.setHeaderLabel("Project Assets")
        self.assets_tree.setRootIsDecorated(True)
        self.assets_tree.setAlternatingRowColors(True)
        self.assets_tree.itemClicked.connect(self._on_asset_clicked)
        layout.addWidget(self.assets_tree)
        
        # Asset management buttons
        button_layout = QHBoxLayout()
        
        self.add_icon_btn = QPushButton("Add Icon")
        self.add_icon_btn.setToolTip("Add a new icon to the project")
        self.add_icon_btn.clicked.connect(self._add_icon)
        button_layout.addWidget(self.add_icon_btn)
        
        self.import_btn = QPushButton("Import")
        self.import_btn.setToolTip("Import icons or images from files")
        self.import_btn.clicked.connect(self._import_assets)
        button_layout.addWidget(self.import_btn)
        
        self.delete_btn = QPushButton("Delete")
        self.delete_btn.setToolTip("Delete selected asset")
        self.delete_btn.clicked.connect(self._delete_asset)
        self.delete_btn.setEnabled(False)  # Disabled until selection
        button_layout.addWidget(self.delete_btn)
        
        layout.addLayout(button_layout)
        
        # Templates section
        templates_group = QGroupBox("Templates")
        templates_layout = QVBoxLayout(templates_group)
        
        self.templates_tree = QTreeWidget()
        self.templates_tree.setHeaderLabel("UI Templates")
        self.templates_tree.setMaximumHeight(200)
        self.templates_tree.itemClicked.connect(self._on_template_clicked)
        templates_layout.addWidget(self.templates_tree)
        
        layout.addWidget(templates_group)
        
        # Asset preview (placeholder for now)
        preview_group = QGroupBox("Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("No asset selected")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(100)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #0B3D91;
                border-radius: 8px;
                background-color: #0B1226;
                color: #AFC9FF;
            }
        """)
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
    
    def _populate_default_content(self):
        """Populate the panel with default icons and templates"""
        # Add default asset categories
        icons_root = QTreeWidgetItem(self.assets_tree, ["Icons"])
        icons_root.setExpanded(True)
        
        # Add some placeholder icons
        placeholder_icons = [
            "battery_full", "battery_half", "battery_empty",
            "wifi_connected", "wifi_disconnected", "bluetooth",
            "arrow_up", "arrow_down", "arrow_left", "arrow_right",
            "menu_icon", "settings_icon", "home_icon"
        ]
        
        for icon_name in placeholder_icons:
            icon_item = QTreeWidgetItem(icons_root, [icon_name])
            icon_item.setData(0, Qt.UserRole, f"icon_{icon_name}")
        
        # Add bitmaps category
        bitmaps_root = QTreeWidgetItem(self.assets_tree, ["Bitmaps"])
        bitmaps_root.setExpanded(True)
        
        # Populate templates
        template_categories = {
            "Basic Layouts": [
                "Header + Menu",
                "Dashboard",
                "Icon Grid",
                "Compact Menu",
                "Fullscreen Menu"
            ],
            "Specialized": [
                "Status Display",
                "Settings Menu",
                "Data Logger",
                "Control Panel"
            ]
        }
        
        for category, templates in template_categories.items():
            category_item = QTreeWidgetItem(self.templates_tree, [category])
            category_item.setExpanded(True)
            
            for template in templates:
                template_item = QTreeWidgetItem(category_item, [template])
                template_item.setData(0, Qt.UserRole, f"template_{template.lower().replace(' ', '_')}")
    
    def _filter_assets(self, text: str):
        """Filter assets based on search text"""
        # Simple text-based filtering
        # This will be enhanced in later milestones
        for i in range(self.assets_tree.topLevelItemCount()):
            item = self.assets_tree.topLevelItem(i)
            self._filter_item_recursive(item, text.lower())
    
    def _filter_item_recursive(self, item: QTreeWidgetItem, filter_text: str):
        """Recursively filter tree items"""
        if not filter_text:
            item.setHidden(False)
            for i in range(item.childCount()):
                self._filter_item_recursive(item.child(i), filter_text)
            return
        
        # Check if item text contains filter
        item_text = item.text(0).lower()
        matches = filter_text in item_text
        
        # Check children
        child_matches = False
        for i in range(item.childCount()):
            child = item.child(i)
            self._filter_item_recursive(child, filter_text)
            if not child.isHidden():
                child_matches = True
        
        # Hide item if it doesn't match and no children match
        item.setHidden(not (matches or child_matches))
    
    def _on_asset_clicked(self, item: QTreeWidgetItem, column: int):
        """Handle asset selection"""
        asset_id = item.data(0, Qt.UserRole)
        if asset_id:
            self.asset_selected.emit(asset_id)
            self.preview_label.setText(f"Selected: {item.text(0)}")
            self.delete_btn.setEnabled(True)
        else:
            self.delete_btn.setEnabled(False)
    
    def _on_template_clicked(self, item: QTreeWidgetItem, column: int):
        """Handle template selection"""
        template_id = item.data(0, Qt.UserRole)
        if template_id:
            self.template_selected.emit(template_id)
            self.preview_label.setText(f"Template: {item.text(0)}")
    
    def _add_icon(self):
        """Add a new icon (placeholder for now)"""
        # This will open the pixel editor in Milestone 2
        print("Add icon clicked - will be implemented in Milestone 2")
    
    def _import_assets(self):
        """Import assets from files (placeholder for now)"""
        # This will open file dialog and import images in Milestone 2
        print("Import assets clicked - will be implemented in Milestone 2")
    
    def _delete_asset(self):
        """Delete the selected asset"""
        current_item = self.assets_tree.currentItem()
        if current_item and current_item.data(0, Qt.UserRole):
            parent = current_item.parent()
            if parent:
                parent.removeChild(current_item)
            else:
                index = self.assets_tree.indexOfTopLevelItem(current_item)
                self.assets_tree.takeTopLevelItem(index)
            
            self.preview_label.setText("No asset selected")
            self.delete_btn.setEnabled(False)
