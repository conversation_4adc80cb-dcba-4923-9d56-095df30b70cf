"""
Inspector panel for editing properties of selected UI elements
Developed by <PERSON>han | SKR Electronics Lab
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox,
    QComboBox, QCheckBox, QPushButton, QGroupBox,
    QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal


class InspectorPanel(QWidget):
    """Panel for inspecting and editing properties of selected elements"""
    
    # Signals for property changes
    property_changed = Signal(str, object)  # property_name, new_value
    
    def __init__(self):
        """Initialize the inspector panel"""
        super().__init__()
        self._current_element = None
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the user interface for the inspector panel"""
        # Main layout with scroll area
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        
        # Create scroll area for properties
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Content widget for scroll area
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setSpacing(8)
        
        # Element info section
        self._create_element_info_section()
        
        # Position and size section
        self._create_position_size_section()
        
        # Appearance section
        self._create_appearance_section()
        
        # Behavior section
        self._create_behavior_section()
        
        # Hardware configuration section
        self._create_hardware_section()
        
        # Add stretch to push everything to top
        self.content_layout.addStretch()
        
        # Set content widget to scroll area
        scroll_area.setWidget(self.content_widget)
        main_layout.addWidget(scroll_area)
        
        # Initially show "no selection" state
        self._show_no_selection()
    
    def _create_element_info_section(self):
        """Create the element information section"""
        self.info_group = QGroupBox("Element Information")
        info_layout = QFormLayout(self.info_group)
        
        # Element type
        self.element_type_label = QLabel("None")
        self.element_type_label.setStyleSheet("font-weight: bold; color: #00B4D8;")
        info_layout.addRow("Type:", self.element_type_label)
        
        # Element name/ID
        self.element_name = QLineEdit()
        self.element_name.setPlaceholderText("Element name...")
        self.element_name.textChanged.connect(lambda text: self._emit_property_change("name", text))
        info_layout.addRow("Name:", self.element_name)
        
        self.content_layout.addWidget(self.info_group)
    
    def _create_position_size_section(self):
        """Create the position and size controls section"""
        self.position_group = QGroupBox("Position & Size")
        pos_layout = QFormLayout(self.position_group)
        
        # Position controls
        pos_row = QHBoxLayout()
        self.x_spin = QSpinBox()
        self.x_spin.setRange(-999, 999)
        self.x_spin.setSuffix(" px")
        self.x_spin.valueChanged.connect(lambda val: self._emit_property_change("x", val))
        pos_row.addWidget(QLabel("X:"))
        pos_row.addWidget(self.x_spin)
        
        self.y_spin = QSpinBox()
        self.y_spin.setRange(-999, 999)
        self.y_spin.setSuffix(" px")
        self.y_spin.valueChanged.connect(lambda val: self._emit_property_change("y", val))
        pos_row.addWidget(QLabel("Y:"))
        pos_row.addWidget(self.y_spin)
        
        pos_layout.addRow("Position:", pos_row)
        
        # Size controls
        size_row = QHBoxLayout()
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 999)
        self.width_spin.setSuffix(" px")
        self.width_spin.valueChanged.connect(lambda val: self._emit_property_change("width", val))
        size_row.addWidget(QLabel("W:"))
        size_row.addWidget(self.width_spin)
        
        self.height_spin = QSpinBox()
        self.height_spin.setRange(1, 999)
        self.height_spin.setSuffix(" px")
        self.height_spin.valueChanged.connect(lambda val: self._emit_property_change("height", val))
        size_row.addWidget(QLabel("H:"))
        size_row.addWidget(self.height_spin)
        
        pos_layout.addRow("Size:", size_row)
        
        self.content_layout.addWidget(self.position_group)
    
    def _create_appearance_section(self):
        """Create the appearance controls section"""
        self.appearance_group = QGroupBox("Appearance")
        appearance_layout = QFormLayout(self.appearance_group)
        
        # Font selection
        self.font_combo = QComboBox()
        self.font_combo.addItems([
            "u8g2_font_6x10_tf",
            "u8g2_font_7x13_tf", 
            "u8g2_font_8x13_tf",
            "u8g2_font_9x15_tf",
            "u8g2_font_10x20_tf"
        ])
        self.font_combo.currentTextChanged.connect(lambda text: self._emit_property_change("font", text))
        appearance_layout.addRow("Font:", self.font_combo)
        
        # Text content (for text elements)
        self.text_content = QLineEdit()
        self.text_content.setPlaceholderText("Text content...")
        self.text_content.textChanged.connect(lambda text: self._emit_property_change("text", text))
        appearance_layout.addRow("Text:", self.text_content)
        
        # Alignment
        self.alignment_combo = QComboBox()
        self.alignment_combo.addItems(["Left", "Center", "Right"])
        self.alignment_combo.currentTextChanged.connect(lambda text: self._emit_property_change("alignment", text))
        appearance_layout.addRow("Alignment:", self.alignment_combo)
        
        # Visibility
        self.visible_check = QCheckBox("Visible")
        self.visible_check.setChecked(True)
        self.visible_check.toggled.connect(lambda checked: self._emit_property_change("visible", checked))
        appearance_layout.addRow("", self.visible_check)
        
        self.content_layout.addWidget(self.appearance_group)
    
    def _create_behavior_section(self):
        """Create the behavior controls section"""
        self.behavior_group = QGroupBox("Behavior")
        behavior_layout = QFormLayout(self.behavior_group)
        
        # Action name (for interactive elements)
        self.action_name = QLineEdit()
        self.action_name.setPlaceholderText("action_function_name")
        self.action_name.textChanged.connect(lambda text: self._emit_property_change("action", text))
        behavior_layout.addRow("Action:", self.action_name)
        
        # Selectable (for menu items)
        self.selectable_check = QCheckBox("Selectable")
        self.selectable_check.toggled.connect(lambda checked: self._emit_property_change("selectable", checked))
        behavior_layout.addRow("", self.selectable_check)
        
        # Menu item properties
        self.menu_index_spin = QSpinBox()
        self.menu_index_spin.setRange(0, 99)
        self.menu_index_spin.valueChanged.connect(lambda val: self._emit_property_change("menu_index", val))
        behavior_layout.addRow("Menu Index:", self.menu_index_spin)
        
        self.content_layout.addWidget(self.behavior_group)
    
    def _create_hardware_section(self):
        """Create the hardware configuration section"""
        self.hardware_group = QGroupBox("Hardware Configuration")
        hardware_layout = QFormLayout(self.hardware_group)
        
        # Display type
        self.display_combo = QComboBox()
        self.display_combo.addItems([
            "SH1106 128x64 (1.3\")",
            "SSD1306 128x64 (1.3\")",
            "SH1106 96x64 (0.96\")",
            "SSD1306 96x64 (0.96\")"
        ])
        self.display_combo.currentTextChanged.connect(lambda text: self._emit_property_change("display_type", text))
        hardware_layout.addRow("Display:", self.display_combo)
        
        # I2C Address
        self.i2c_address = QLineEdit("0x3C")
        self.i2c_address.textChanged.connect(lambda text: self._emit_property_change("i2c_address", text))
        hardware_layout.addRow("I2C Address:", self.i2c_address)
        
        # Pin configuration
        pin_row = QHBoxLayout()
        self.sda_pin = QSpinBox()
        self.sda_pin.setRange(0, 40)
        self.sda_pin.setValue(21)  # ESP32 default
        self.sda_pin.valueChanged.connect(lambda val: self._emit_property_change("sda_pin", val))
        pin_row.addWidget(QLabel("SDA:"))
        pin_row.addWidget(self.sda_pin)
        
        self.scl_pin = QSpinBox()
        self.scl_pin.setRange(0, 40)
        self.scl_pin.setValue(22)  # ESP32 default
        self.scl_pin.valueChanged.connect(lambda val: self._emit_property_change("scl_pin", val))
        pin_row.addWidget(QLabel("SCL:"))
        pin_row.addWidget(self.scl_pin)
        
        hardware_layout.addRow("I2C Pins:", pin_row)
        
        # Target board
        self.target_combo = QComboBox()
        self.target_combo.addItems([
            "ESP32",
            "ESP8266", 
            "Arduino UNO",
            "Arduino Nano"
        ])
        self.target_combo.currentTextChanged.connect(lambda text: self._emit_property_change("target_board", text))
        hardware_layout.addRow("Target Board:", self.target_combo)
        
        self.content_layout.addWidget(self.hardware_group)
    
    def _emit_property_change(self, property_name: str, value):
        """Emit property change signal"""
        if self._current_element is not None:
            self.property_changed.emit(property_name, value)
    
    def _show_no_selection(self):
        """Show the no selection state"""
        self.element_type_label.setText("No element selected")
        self._set_controls_enabled(False)
    
    def _set_controls_enabled(self, enabled: bool):
        """Enable or disable all controls"""
        for group in [self.info_group, self.position_group, self.appearance_group, 
                     self.behavior_group, self.hardware_group]:
            group.setEnabled(enabled)
    
    def set_selected_element(self, element_data: dict):
        """Set the currently selected element and update controls"""
        self._current_element = element_data
        
        if element_data is None:
            self._show_no_selection()
            return
        
        # Enable controls
        self._set_controls_enabled(True)
        
        # Update element type
        element_type = element_data.get("type", "Unknown")
        self.element_type_label.setText(element_type)
        
        # Update controls with element data
        self.element_name.setText(element_data.get("name", ""))
        self.x_spin.setValue(element_data.get("x", 0))
        self.y_spin.setValue(element_data.get("y", 0))
        self.width_spin.setValue(element_data.get("width", 16))
        self.height_spin.setValue(element_data.get("height", 16))
        
        # Update appearance controls
        font = element_data.get("font", "u8g2_font_6x10_tf")
        if font in [self.font_combo.itemText(i) for i in range(self.font_combo.count())]:
            self.font_combo.setCurrentText(font)
        
        self.text_content.setText(element_data.get("text", ""))
        self.alignment_combo.setCurrentText(element_data.get("alignment", "Left"))
        self.visible_check.setChecked(element_data.get("visible", True))
        
        # Update behavior controls
        self.action_name.setText(element_data.get("action", ""))
        self.selectable_check.setChecked(element_data.get("selectable", False))
        self.menu_index_spin.setValue(element_data.get("menu_index", 0))
        
        # Update hardware controls
        display_type = element_data.get("display_type", "SH1106 128x64 (1.3\")")
        if display_type in [self.display_combo.itemText(i) for i in range(self.display_combo.count())]:
            self.display_combo.setCurrentText(display_type)
        
        self.i2c_address.setText(element_data.get("i2c_address", "0x3C"))
        self.sda_pin.setValue(element_data.get("sda_pin", 21))
        self.scl_pin.setValue(element_data.get("scl_pin", 22))
        
        target_board = element_data.get("target_board", "ESP32")
        if target_board in [self.target_combo.itemText(i) for i in range(self.target_combo.count())]:
            self.target_combo.setCurrentText(target_board)
