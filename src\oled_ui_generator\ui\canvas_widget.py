"""
Canvas widget for OLED display simulation and UI design
Developed by <PERSON> | SKR Electronics Lab
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QComboBox, QSlider, QFrame
)
from PySide6.QtCore import Qt, Signal, QRect, QPoint
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPixmap


class OLEDSimulator(QWidget):
    """Widget that simulates an OLED display with pixel-perfect rendering"""
    
    # Signals for interaction
    pixel_clicked = Signal(int, int)  # x, y coordinates
    element_selected = Signal(dict)   # element data
    
    def __init__(self, width: int = 128, height: int = 64):
        """Initialize the OLED simulator"""
        super().__init__()
        
        # Display properties
        self.display_width = width
        self.display_height = height
        self.pixel_size = 4  # Size of each pixel in screen pixels
        self.border_size = 2
        
        # Display data (1-bit per pixel, True = on/white, False = off/black)
        self.pixels = [[False for _ in range(width)] for _ in range(height)]
        
        # UI elements on the display
        self.ui_elements = []
        self.selected_element = None
        
        # Set widget size
        total_width = width * self.pixel_size + 2 * self.border_size
        total_height = height * self.pixel_size + 2 * self.border_size
        self.setFixedSize(total_width, total_height)
        
        # Enable mouse tracking for interactions
        self.setMouseTracking(True)
        
        # Draw some demo content
        self._draw_demo_content()
    
    def paintEvent(self, event):
        """Paint the OLED display simulation"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing, False)  # Pixel-perfect rendering
        
        # Draw the display border (simulating the physical display)
        border_color = QColor("#333333")
        painter.fillRect(self.rect(), border_color)
        
        # Calculate display area
        display_rect = QRect(
            self.border_size, 
            self.border_size,
            self.display_width * self.pixel_size,
            self.display_height * self.pixel_size
        )
        
        # Fill display background (black for OLED)
        painter.fillRect(display_rect, QColor("#000000"))
        
        # Draw pixels
        pixel_brush = QBrush(QColor("#FFFFFF"))  # White pixels for OLED
        painter.setBrush(pixel_brush)
        painter.setPen(Qt.NoPen)
        
        for y in range(self.display_height):
            for x in range(self.display_width):
                if self.pixels[y][x]:  # Pixel is on
                    pixel_rect = QRect(
                        self.border_size + x * self.pixel_size,
                        self.border_size + y * self.pixel_size,
                        self.pixel_size,
                        self.pixel_size
                    )
                    painter.drawRect(pixel_rect)
        
        # Draw selection highlight if an element is selected
        if self.selected_element:
            self._draw_selection_highlight(painter)
    
    def _draw_selection_highlight(self, painter: QPainter):
        """Draw highlight around selected element"""
        if not self.selected_element:
            return
        
        element = self.selected_element
        x = element.get("x", 0)
        y = element.get("y", 0)
        width = element.get("width", 16)
        height = element.get("height", 16)
        
        # Draw selection rectangle
        selection_rect = QRect(
            self.border_size + x * self.pixel_size - 1,
            self.border_size + y * self.pixel_size - 1,
            width * self.pixel_size + 2,
            height * self.pixel_size + 2
        )
        
        painter.setPen(QPen(QColor("#00B4D8"), 2, Qt.DashLine))
        painter.setBrush(Qt.NoBrush)
        painter.drawRect(selection_rect)
    
    def mousePressEvent(self, event):
        """Handle mouse clicks on the display"""
        if event.button() == Qt.LeftButton:
            # Convert screen coordinates to display pixel coordinates
            x = (event.x() - self.border_size) // self.pixel_size
            y = (event.y() - self.border_size) // self.pixel_size
            
            # Check bounds
            if 0 <= x < self.display_width and 0 <= y < self.display_height:
                self.pixel_clicked.emit(x, y)
                
                # Check if we clicked on a UI element
                clicked_element = self._find_element_at_position(x, y)
                if clicked_element:
                    self.selected_element = clicked_element
                    self.element_selected.emit(clicked_element)
                    self.update()  # Redraw to show selection
    
    def _find_element_at_position(self, x: int, y: int) -> dict:
        """Find UI element at the given position"""
        for element in self.ui_elements:
            elem_x = element.get("x", 0)
            elem_y = element.get("y", 0)
            elem_width = element.get("width", 16)
            elem_height = element.get("height", 16)
            
            if (elem_x <= x < elem_x + elem_width and 
                elem_y <= y < elem_y + elem_height):
                return element
        
        return None
    
    def clear_display(self):
        """Clear all pixels on the display"""
        for y in range(self.display_height):
            for x in range(self.display_width):
                self.pixels[y][x] = False
        self.update()
    
    def set_pixel(self, x: int, y: int, state: bool):
        """Set a single pixel state"""
        if 0 <= x < self.display_width and 0 <= y < self.display_height:
            self.pixels[y][x] = state
    
    def draw_text(self, x: int, y: int, text: str, font_size: int = 8):
        """Draw text on the display (simplified simulation)"""
        # This is a simplified text rendering for demo purposes
        # In the full implementation, this would use actual font rendering
        char_width = 6
        char_height = 8
        
        for i, char in enumerate(text):
            char_x = x + i * char_width
            if char_x >= self.display_width:
                break
            
            # Draw a simple character representation
            self._draw_simple_char(char_x, y, char, char_width, char_height)
    
    def _draw_simple_char(self, x: int, y: int, char: str, width: int, height: int):
        """Draw a simple character representation"""
        # This is a very basic character rendering for demo
        # Just draw a rectangle for now
        for dy in range(min(height, self.display_height - y)):
            for dx in range(min(width - 1, self.display_width - x)):  # Leave space between chars
                if y + dy < self.display_height and x + dx < self.display_width:
                    # Simple pattern based on character
                    if char.isalnum() and (dx == 0 or dy == 0 or dx == width-2 or dy == height-1):
                        self.pixels[y + dy][x + dx] = True
    
    def _draw_demo_content(self):
        """Draw some demo content to show the display working"""
        # Clear display first
        self.clear_display()
        
        # Draw header text
        self.draw_text(2, 2, "OLED UI Generator")
        
        # Draw a simple menu
        menu_items = ["Settings", "Display", "About"]
        for i, item in enumerate(menu_items):
            y_pos = 20 + i * 12
            self.draw_text(10, y_pos, f"> {item}")
        
        # Add some UI elements for interaction
        self.ui_elements = [
            {
                "type": "text",
                "name": "header",
                "x": 2, "y": 2,
                "width": 120, "height": 10,
                "text": "OLED UI Generator",
                "font": "u8g2_font_6x10_tf",
                "visible": True
            },
            {
                "type": "menu_item",
                "name": "menu_settings",
                "x": 10, "y": 20,
                "width": 60, "height": 10,
                "text": "> Settings",
                "action": "open_settings",
                "selectable": True,
                "menu_index": 0
            },
            {
                "type": "menu_item", 
                "name": "menu_display",
                "x": 10, "y": 32,
                "width": 60, "height": 10,
                "text": "> Display",
                "action": "open_display",
                "selectable": True,
                "menu_index": 1
            },
            {
                "type": "menu_item",
                "name": "menu_about", 
                "x": 10, "y": 44,
                "width": 60, "height": 10,
                "text": "> About",
                "action": "open_about",
                "selectable": True,
                "menu_index": 2
            }
        ]
        
        self.update()
    
    def resize_display(self, width: int, height: int):
        """Resize the display dimensions"""
        self.display_width = width
        self.display_height = height
        
        # Recreate pixel array
        self.pixels = [[False for _ in range(width)] for _ in range(height)]
        
        # Update widget size
        total_width = width * self.pixel_size + 2 * self.border_size
        total_height = height * self.pixel_size + 2 * self.border_size
        self.setFixedSize(total_width, total_height)
        
        # Redraw demo content
        self._draw_demo_content()


class CanvasWidget(QWidget):
    """Main canvas widget containing OLED simulator and controls"""
    
    def __init__(self):
        """Initialize the canvas widget"""
        super().__init__()
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the canvas user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # Controls row
        controls_layout = QHBoxLayout()
        
        # Display size selection
        controls_layout.addWidget(QLabel("Display:"))
        self.display_combo = QComboBox()
        self.display_combo.addItems([
            "SH1106 128x64 (1.3\")",
            "SSD1306 128x64 (1.3\")", 
            "SH1106 96x64 (0.96\")",
            "SSD1306 96x64 (0.96\")"
        ])
        self.display_combo.currentTextChanged.connect(self._on_display_changed)
        controls_layout.addWidget(self.display_combo)
        
        controls_layout.addStretch()
        
        # Zoom controls
        controls_layout.addWidget(QLabel("Zoom:"))
        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setRange(1, 8)
        self.zoom_slider.setValue(4)
        self.zoom_slider.valueChanged.connect(self._on_zoom_changed)
        controls_layout.addWidget(self.zoom_slider)
        
        self.zoom_label = QLabel("4x")
        controls_layout.addWidget(self.zoom_label)
        
        # Clear button
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self._clear_display)
        controls_layout.addWidget(self.clear_btn)
        
        layout.addLayout(controls_layout)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)
        
        # OLED simulator (centered)
        simulator_layout = QHBoxLayout()
        simulator_layout.addStretch()
        
        self.oled_simulator = OLEDSimulator(128, 64)
        self.oled_simulator.element_selected.connect(self._on_element_selected)
        simulator_layout.addWidget(self.oled_simulator)
        
        simulator_layout.addStretch()
        layout.addLayout(simulator_layout)
        
        layout.addStretch()
    
    def _on_display_changed(self, display_type: str):
        """Handle display type change"""
        if "96x64" in display_type:
            self.oled_simulator.resize_display(96, 64)
        else:
            self.oled_simulator.resize_display(128, 64)
    
    def _on_zoom_changed(self, zoom_level: int):
        """Handle zoom level change"""
        self.oled_simulator.pixel_size = zoom_level
        self.zoom_label.setText(f"{zoom_level}x")
        
        # Update simulator size
        total_width = (self.oled_simulator.display_width * zoom_level + 
                      2 * self.oled_simulator.border_size)
        total_height = (self.oled_simulator.display_height * zoom_level + 
                       2 * self.oled_simulator.border_size)
        self.oled_simulator.setFixedSize(total_width, total_height)
        self.oled_simulator.update()
    
    def _clear_display(self):
        """Clear the OLED display"""
        self.oled_simulator.clear_display()
    
    def _on_element_selected(self, element_data: dict):
        """Handle element selection from simulator"""
        # This signal can be connected to the inspector panel
        print(f"Element selected: {element_data.get('name', 'Unknown')}")
        # In the full implementation, this would update the inspector panel
