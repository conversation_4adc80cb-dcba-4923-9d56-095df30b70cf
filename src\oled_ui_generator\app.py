"""
Application bootstrap and theme loader for OLED Display UI Generator
Developed by <PERSON>han | SKR Electronics Lab
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QSettings
from PySide6.QtGui import QPalette, QColor

from oled_ui_generator.window import MainWindow


class OLEDUIGeneratorApp:
    """Main application class that handles initialization and theming"""
    
    def __init__(self):
        """Initialize the application with dark theme"""
        self.settings = QSettings()
        self.main_window = None
        
        # Apply the dark theme
        self._apply_dark_theme()
        
        # Create the main window
        self.main_window = MainWindow()
    
    def show(self):
        """Show the main application window"""
        if self.main_window:
            self.main_window.show()
    
    def _apply_dark_theme(self):
        """Apply the professional dark theme as specified in requirements"""
        app = QApplication.instance()
        
        # Define the color palette based on requirements
        # Primary palette from prompt:
        # Background / primary surface: #0B1226 (very dark blue/indigo)
        # Panel surface: #122040 (secondary dark)
        # Primary accent: #0B3D91 (deep blue)
        # Secondary accent: #00B4D8 (cyan)
        # Muted text: #AFC9FF
        # Bright text: #FFFFFF
        # Highlight / interactive: #6A2AC5 (a subtle purple emphasis)
        # Error / alert: #FF5C5C
        
        palette = QPalette()
        
        # Window colors
        palette.setColor(QPalette.Window, QColor("#0B1226"))  # Background
        palette.setColor(QPalette.WindowText, QColor("#FFFFFF"))  # Bright text
        
        # Base colors (for input fields, etc.)
        palette.setColor(QPalette.Base, QColor("#122040"))  # Panel surface
        palette.setColor(QPalette.AlternateBase, QColor("#0B3D91"))  # Primary accent
        
        # Text colors
        palette.setColor(QPalette.Text, QColor("#FFFFFF"))  # Bright text
        palette.setColor(QPalette.BrightText, QColor("#FFFFFF"))
        palette.setColor(QPalette.PlaceholderText, QColor("#AFC9FF"))  # Muted text
        
        # Button colors
        palette.setColor(QPalette.Button, QColor("#122040"))  # Panel surface
        palette.setColor(QPalette.ButtonText, QColor("#FFFFFF"))  # Bright text
        
        # Highlight colors
        palette.setColor(QPalette.Highlight, QColor("#6A2AC5"))  # Purple emphasis
        palette.setColor(QPalette.HighlightedText, QColor("#FFFFFF"))
        
        # Apply the palette
        app.setPalette(palette)
        
        # Apply additional stylesheet for modern look
        stylesheet = """
        QMainWindow {
            background-color: #0B1226;
            color: #FFFFFF;
        }
        
        QDockWidget {
            background-color: #122040;
            color: #FFFFFF;
            border: 1px solid #0B3D91;
            border-radius: 8px;
        }
        
        QDockWidget::title {
            background-color: #0B3D91;
            color: #FFFFFF;
            padding: 8px;
            border-radius: 4px;
        }
        
        QToolBar {
            background-color: #122040;
            border: 1px solid #0B3D91;
            spacing: 4px;
            padding: 4px;
        }
        
        QMenuBar {
            background-color: #122040;
            color: #FFFFFF;
            border-bottom: 1px solid #0B3D91;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #6A2AC5;
            border-radius: 4px;
        }
        
        QPushButton {
            background-color: #122040;
            color: #FFFFFF;
            border: 1px solid #0B3D91;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #6A2AC5;
            border-color: #00B4D8;
        }
        
        QPushButton:pressed {
            background-color: #0B3D91;
        }
        
        QTabWidget::pane {
            border: 1px solid #0B3D91;
            background-color: #122040;
            border-radius: 8px;
        }
        
        QTabBar::tab {
            background-color: #0B1226;
            color: #AFC9FF;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        
        QTabBar::tab:selected {
            background-color: #122040;
            color: #FFFFFF;
            border-bottom: 2px solid #00B4D8;
        }
        
        QStatusBar {
            background-color: #122040;
            color: #AFC9FF;
            border-top: 1px solid #0B3D91;
        }
        
        QTextEdit, QPlainTextEdit {
            background-color: #0B1226;
            color: #FFFFFF;
            border: 1px solid #0B3D91;
            border-radius: 4px;
            padding: 4px;
        }
        
        QScrollBar:vertical {
            background-color: #122040;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #6A2AC5;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #00B4D8;
        }
        """
        
        app.setStyleSheet(stylesheet)
