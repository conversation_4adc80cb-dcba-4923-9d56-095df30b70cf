#!/usr/bin/env python3
"""
Main entry point for OLED Display UI Generator
Developed by SK Raihan | SKR Electronics Lab
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path for development
src_dir = Path(__file__).parent.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from oled_ui_generator.app import OLEDUIGeneratorApp


def main():
    """Main application entry point"""
    # Enable high DPI scaling for modern displays
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # Create the application instance
    app = QApplication(sys.argv)
    app.setApplicationName("OLED Display UI Generator")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("SKR Electronics Lab")
    app.setOrganizationDomain("skrelectronicslab.com")
    
    # Set application icon if available
    icon_path = Path(__file__).parent / "resources" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Create and show the main application window
    main_app = OLEDUIGeneratorApp()
    main_app.show()
    
    # Start the event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
