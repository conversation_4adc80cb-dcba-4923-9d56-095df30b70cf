# OLED Display UI Generator

A professional desktop application for designing OLED UIs and generating Arduino/ESP code.

**Developed by <PERSON> | SKR Electronics Lab**

## Overview

The OLED Display UI Generator is a comprehensive desktop application built with PySide6 that allows you to:

- Design user interfaces for OLED displays (0.96" and 1.3" sizes)
- Create and edit pixel-perfect icons with advanced image processing
- Generate ready-to-use Arduino/ESP code with proper memory management
- Support multiple display controllers (SH1106, SSD1306)
- Export projects for various target boards (ESP32, ESP8266, Arduino UNO/Nano)

## Features

### Current Implementation (Milestone 1)
- ✅ Professional dark-themed UI inspired by Premiere/Photoshop/DaVinci Resolve
- ✅ Dockable panels: Assets, Inspector, Code Preview & Console
- ✅ Tabbed interface: Icons & Image → Code, OLED UI Designer
- ✅ OLED display simulator with interactive elements
- ✅ Property inspector for editing element attributes
- ✅ Workspace management with layout saving/loading
- ✅ Status bar with project info and branding

### Planned Features (Upcoming Milestones)
- 🔄 Pixel editor with undo/redo and image import (Milestone 2)
- 🔄 Advanced image processing with dithering algorithms (Milestone 2)
- 🔄 Drag & drop UI designer with templates (Milestone 3)
- 🔄 Code generation engine with Jinja2 templates (Milestone 4)
- 🔄 Memory estimation and AVR optimization (Milestone 4)
- 🔄 Live code preview and export functionality (Milestone 5)
- 🔄 Unit tests and example projects (Milestone 6)

## Installation & Setup

### Prerequisites
- Python 3.11 or higher
- pip package manager

### Installation Steps

1. **Clone or extract the project:**
   ```bash
   cd oled-display-ui-generator
   ```

2. **Create a virtual environment:**
   ```bash
   python -m venv venv
   ```

3. **Activate the virtual environment:**
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`

4. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

5. **Run the application:**
   ```bash
   python -m oled_ui_generator.main
   ```

## Usage

### Getting Started

1. **Launch the application** using the command above
2. **Explore the interface:**
   - Left panel: Assets & Templates browser
   - Center: Tabbed canvas with OLED simulator
   - Right panel: Inspector for editing properties
   - Bottom: Code preview and console output

3. **Interact with the OLED simulator:**
   - Click on elements to select them
   - Use the Inspector panel to modify properties
   - Change display type and zoom level using controls

### Interface Overview

- **Menu Bar**: File operations, edit functions, view options, tools, and help
- **Toolbar**: Quick access to common actions
- **Assets Panel**: Manage icons, bitmaps, and templates
- **Canvas**: OLED display simulator with zoom and display type controls
- **Inspector**: Edit properties of selected elements
- **Code Preview**: View generated code (will be implemented in Milestone 4)
- **Status Bar**: Shows current project, display resolution, and branding

## Project Structure

```
oled-display-ui-generator/
├── src/
│   └── oled_ui_generator/
│       ├── __init__.py          # Package initialization
│       ├── main.py              # Application entry point
│       ├── app.py               # Application bootstrap & theming
│       ├── window.py            # Main window with docks & menus
│       ├── workspace.py         # Workspace state management
│       └── ui/                  # UI components
│           ├── __init__.py
│           ├── assets_panel.py  # Assets & templates browser
│           ├── inspector.py     # Properties inspector
│           └── canvas_widget.py # OLED simulator & canvas
├── requirements.txt             # Python dependencies
└── README.md                   # This file
```

## Development Milestones

### ✅ Milestone 1: App skeleton & theme (COMPLETED)
- PySide6 application with professional dark theme
- Main window with toolbar, docks, and status bar
- OLED simulator with basic interaction
- Workspace management system

### 🔄 Milestone 2: Pixel editor (NEXT)
- Pixel editor with drawing tools
- Image import with dithering algorithms
- Icon export to PROGMEM/XBM formats
- Asset management system

### 🔄 Milestone 3: Canvas & UI Designer
- Drag & drop UI elements
- Template gallery
- Menu builder with simulation
- Advanced inspector controls

### 🔄 Milestone 4: Code generation engine
- Jinja2 template system
- Memory estimation for AVR boards
- Support for U8g2 and Adafruit libraries
- Export to Arduino/PlatformIO projects

### 🔄 Milestone 5: Polishing & packaging
- Live code preview
- Tooltips and keyboard shortcuts
- Settings panel
- PyInstaller build scripts

### 🔄 Milestone 6: Tests & examples
- Unit tests with pytest
- Example projects
- Documentation

## Technical Details

### Supported Hardware
- **Displays**: 0.96" and 1.3" OLED (SH1106, SSD1306 controllers)
- **Target Boards**: ESP32, ESP8266, Arduino UNO, Arduino Nano
- **Libraries**: U8g2 (primary), Adafruit_SSD1306 + Adafruit_GFX

### Theme Colors
- Background: #0B1226 (dark blue/indigo)
- Panel surface: #122040 (secondary dark)
- Primary accent: #0B3D91 (deep blue)
- Secondary accent: #00B4D8 (cyan)
- Highlight: #6A2AC5 (purple emphasis)
- Text: #FFFFFF (bright), #AFC9FF (muted)

## Testing Milestone 1

To verify Milestone 1 is working correctly:

1. **Run the application:**
   ```bash
   python -m oled_ui_generator.main
   ```

2. **Verify the following:**
   - ✅ Application opens with dark blue theme
   - ✅ Status bar shows "Developed by SK Raihan | SKR Electronics Lab"
   - ✅ All dock panels are visible and dockable
   - ✅ OLED simulator shows demo content
   - ✅ Clicking on simulator elements shows selection
   - ✅ Inspector panel updates when elements are selected
   - ✅ Display type and zoom controls work
   - ✅ Window state is saved when closing

## License

Proprietary software - All rights reserved.
Developed by SK Raihan | SKR Electronics Lab

## Support

For support and questions, please contact SK Raihan | SKR Electronics Lab.

---

**Current Version**: 1.0.0 (Milestone 1)  
**Last Updated**: 2025-08-26
