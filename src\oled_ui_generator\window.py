"""
Main window implementation for OLED Display UI Generator
Developed by <PERSON> | SKR Electronics Lab
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QDockWidget, QTabWidget, QTextEdit, QLabel,
    QMenuBar, QToolBar, QStatusBar, QSplitter
)
from PySide6.QtCore import Qt, QSettings
from PySide6.QtGui import QAction, QIcon

from oled_ui_generator.ui.assets_panel import AssetsPanel
from oled_ui_generator.ui.inspector import InspectorPanel
from oled_ui_generator.ui.canvas_widget import CanvasWidget


class MainWindow(QMainWindow):
    """Main application window with docks, toolbar, and tabbed interface"""
    
    def __init__(self):
        """Initialize the main window with all UI components"""
        super().__init__()
        
        # Window properties
        self.setWindowTitle("OLED Display UI Generator")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # Settings for saving window state
        self.settings = QSettings()
        
        # Initialize UI components
        self._create_menu_bar()
        self._create_toolbar()
        self._create_central_widget()
        self._create_dock_widgets()
        self._create_status_bar()
        
        # Restore window state if available
        self._restore_window_state()
    
    def _create_menu_bar(self):
        """Create the application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # New project action
        new_action = QAction("&New Project", self)
        new_action.setShortcut("Ctrl+N")
        new_action.setStatusTip("Create a new OLED UI project")
        file_menu.addAction(new_action)
        
        # Open project action
        open_action = QAction("&Open Project", self)
        open_action.setShortcut("Ctrl+O")
        open_action.setStatusTip("Open an existing project")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # Save actions
        save_action = QAction("&Save", self)
        save_action.setShortcut("Ctrl+S")
        save_action.setStatusTip("Save the current project")
        file_menu.addAction(save_action)
        
        save_as_action = QAction("Save &As...", self)
        save_as_action.setShortcut("Ctrl+Shift+S")
        save_as_action.setStatusTip("Save the project with a new name")
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # Export actions
        export_code_action = QAction("Export &Code", self)
        export_code_action.setShortcut("Ctrl+E")
        export_code_action.setStatusTip("Export Arduino/ESP code")
        file_menu.addAction(export_code_action)
        
        export_icon_action = QAction("Export &Icon", self)
        export_icon_action.setStatusTip("Export current icon as PROGMEM or bitmap")
        file_menu.addAction(export_icon_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("&Edit")
        
        undo_action = QAction("&Undo", self)
        undo_action.setShortcut("Ctrl+Z")
        undo_action.setStatusTip("Undo the last action")
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("&Redo", self)
        redo_action.setShortcut("Ctrl+Y")
        redo_action.setStatusTip("Redo the last undone action")
        edit_menu.addAction(redo_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        preview_action = QAction("&Preview", self)
        preview_action.setShortcut("F5")
        preview_action.setStatusTip("Preview the current design")
        view_menu.addAction(preview_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        settings_action = QAction("&Settings", self)
        settings_action.setStatusTip("Open application settings")
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        about_action = QAction("&About", self)
        about_action.setStatusTip("About OLED Display UI Generator")
        help_menu.addAction(about_action)
    
    def _create_toolbar(self):
        """Create the main toolbar with common actions"""
        toolbar = self.addToolBar("Main Toolbar")
        toolbar.setObjectName("MainToolbar")  # For state saving
        
        # Add toolbar actions (icons would be added later)
        toolbar.addAction("New")
        toolbar.addAction("Open")
        toolbar.addAction("Save")
        toolbar.addSeparator()
        toolbar.addAction("Undo")
        toolbar.addAction("Redo")
        toolbar.addSeparator()
        toolbar.addAction("Export Code")
        toolbar.addAction("Export Icon")
        toolbar.addSeparator()
        toolbar.addAction("Preview")
        toolbar.addAction("Settings")
    
    def _create_central_widget(self):
        """Create the central tabbed widget area"""
        # Create the main tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(True)
        
        # Create placeholder tabs for now
        # Icons & Image → Code tab
        icons_tab = QWidget()
        icons_layout = QVBoxLayout(icons_tab)
        icons_layout.addWidget(QLabel("Icons & Image → Code Tab\n(Pixel Editor will be implemented in Milestone 2)"))
        self.tab_widget.addTab(icons_tab, "Icons & Image → Code")
        
        # OLED UI Designer tab
        ui_designer_tab = QWidget()
        ui_layout = QVBoxLayout(ui_designer_tab)
        
        # Add canvas widget for OLED simulation
        self.canvas_widget = CanvasWidget()
        ui_layout.addWidget(self.canvas_widget)
        
        self.tab_widget.addTab(ui_designer_tab, "OLED UI Designer")
        
        # Set as central widget
        self.setCentralWidget(self.tab_widget)
    
    def _create_dock_widgets(self):
        """Create the dockable panels"""
        # Left dock: Project Explorer / Assets
        self.assets_dock = QDockWidget("Assets & Templates", self)
        self.assets_dock.setObjectName("AssetsDock")  # For state saving
        self.assets_panel = AssetsPanel()
        self.assets_dock.setWidget(self.assets_panel)
        self.addDockWidget(Qt.LeftDockWidgetArea, self.assets_dock)
        
        # Right dock: Inspector / Properties
        self.inspector_dock = QDockWidget("Inspector", self)
        self.inspector_dock.setObjectName("InspectorDock")  # For state saving
        self.inspector_panel = InspectorPanel()
        self.inspector_dock.setWidget(self.inspector_panel)
        self.addDockWidget(Qt.RightDockWidgetArea, self.inspector_dock)
        
        # Bottom dock: Live Code Preview + Console
        self.bottom_dock = QDockWidget("Code Preview & Console", self)
        self.bottom_dock.setObjectName("BottomDock")  # For state saving
        
        # Create splitter for code preview and console
        bottom_splitter = QSplitter(Qt.Horizontal)
        
        # Live code preview
        self.code_preview = QTextEdit()
        self.code_preview.setPlaceholderText("Generated code will appear here...")
        self.code_preview.setReadOnly(True)
        bottom_splitter.addWidget(self.code_preview)
        
        # Console/logs
        self.console = QTextEdit()
        self.console.setPlaceholderText("Console output and logs...")
        self.console.setMaximumHeight(200)
        bottom_splitter.addWidget(self.console)
        
        # Set splitter proportions
        bottom_splitter.setSizes([600, 200])
        
        self.bottom_dock.setWidget(bottom_splitter)
        self.addDockWidget(Qt.BottomDockWidgetArea, self.bottom_dock)
    
    def _create_status_bar(self):
        """Create the status bar with project info and branding"""
        status_bar = self.statusBar()
        
        # Left side: current project and target info
        self.project_label = QLabel("No project loaded")
        status_bar.addWidget(self.project_label)
        
        # Add stretch to push branding to the right
        status_bar.addWidget(QLabel(), 1)  # Stretch widget
        
        # Right side: branding and current simulator resolution
        self.resolution_label = QLabel("128×64 (SH1106)")
        status_bar.addWidget(self.resolution_label)
        
        # Separator
        status_bar.addWidget(QLabel(" | "))
        
        # Branding (as required in prompt)
        branding_label = QLabel("Developed by SK Raihan | SKR Electronics Lab")
        branding_label.setStyleSheet("color: #00B4D8; font-weight: bold;")
        status_bar.addWidget(branding_label)
    
    def _restore_window_state(self):
        """Restore window geometry and dock positions from settings"""
        self.restoreGeometry(self.settings.value("geometry", b""))
        self.restoreState(self.settings.value("windowState", b""))
    
    def closeEvent(self, event):
        """Save window state when closing"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
        super().closeEvent(event)
